# ASN.1 parser generation functions
# This file contains the core functions for generating ASN.1 parsers

# Function to generate ASN.1 parser for a specific protocol
function(generate_asn1_parser PROTOCOL_NAME)
    # Use absolute paths to work from any calling directory
    set(ASN1_SOURCE_DIR "${CMAKE_SOURCE_DIR}/test/yaLiteDpi/plugins/asn1")
    set(PROTOCOL_DIR "${ASN1_SOURCE_DIR}/protocols/${PROTOCOL_NAME}")
    set(GENERATED_DIR "${CMAKE_BINARY_DIR}/test/yaLiteDpi/plugins/asn1/generated/${PROTOCOL_NAME}")

    # Find ASN.1 files for this protocol
    file(GLOB ASN1_FILES "${PROTOCOL_DIR}/*.asn")

    if(NOT ASN1_FILES)
        message(WARNING "No ASN.1 files found for protocol ${PROTOCOL_NAME} in ${PROTOCOL_DIR}")
        return()
    endif()

    message(STATUS "Found ASN.1 files for ${PROTOCOL_NAME}: ${ASN1_FILES}")

    # Create output directory
    file(MAKE_DIRECTORY ${GENERATED_DIR})

    # Complete list of all generated files
    set(EXPECTED_GENERATED_FILES
        # Core message types
        ${GENERATED_DIR}/Message.c
        ${GENERATED_DIR}/PDUs.c
        ${GENERATED_DIR}/PDU.c
        ${GENERATED_DIR}/BulkPDU.c
        ${GENERATED_DIR}/VarBind.c
        ${GENERATED_DIR}/VarBindList.c
        ${GENERATED_DIR}/ObjectName.c
        ${GENERATED_DIR}/ObjectSyntax.c
        ${GENERATED_DIR}/SimpleSyntax.c
        ${GENERATED_DIR}/ApplicationSyntax.c
        ${GENERATED_DIR}/Trap-PDU.c
        # Application types
        ${GENERATED_DIR}/NetworkAddress.c
        ${GENERATED_DIR}/Counter.c
        ${GENERATED_DIR}/Gauge.c
        ${GENERATED_DIR}/TimeTicks.c
        ${GENERATED_DIR}/Opaque.c

        # Basic ASN.1 types
        ${GENERATED_DIR}/INTEGER.c
        ${GENERATED_DIR}/OCTET_STRING.c
        ${GENERATED_DIR}/OBJECT_IDENTIFIER.c
        ${GENERATED_DIR}/NULL.c
        ${GENERATED_DIR}/NativeInteger.c
        ${GENERATED_DIR}/BIT_STRING.c
        # ASN.1 framework
        ${GENERATED_DIR}/ber_decoder.c
        ${GENERATED_DIR}/ber_tlv_length.c
        ${GENERATED_DIR}/ber_tlv_tag.c
        ${GENERATED_DIR}/der_encoder.c
        ${GENERATED_DIR}/constr_CHOICE.c
        ${GENERATED_DIR}/constr_SEQUENCE.c
        ${GENERATED_DIR}/constr_SEQUENCE_OF.c
        ${GENERATED_DIR}/constr_SET_OF.c
        ${GENERATED_DIR}/constr_TYPE.c
        ${GENERATED_DIR}/constraints.c
        ${GENERATED_DIR}/asn_application.c
        ${GENERATED_DIR}/asn_codecs_prim.c
        ${GENERATED_DIR}/asn_internal.c
        ${GENERATED_DIR}/asn_SEQUENCE_OF.c
        ${GENERATED_DIR}/asn_SET_OF.c
        ${GENERATED_DIR}/asn_bit_data.c
        ${GENERATED_DIR}/asn_random_fill.c
        ${GENERATED_DIR}/xer_decoder.c
        ${GENERATED_DIR}/xer_encoder.c
        ${GENERATED_DIR}/xer_support.c
        ${GENERATED_DIR}/per_decoder.c
        ${GENERATED_DIR}/per_encoder.c
        ${GENERATED_DIR}/per_support.c
        ${GENERATED_DIR}/per_opentype.c
        ${GENERATED_DIR}/oer_decoder.c
        ${GENERATED_DIR}/oer_encoder.c
        ${GENERATED_DIR}/oer_support.c
        # OER support files
        ${GENERATED_DIR}/OPEN_TYPE.c
        ${GENERATED_DIR}/INTEGER_oer.c
        ${GENERATED_DIR}/BIT_STRING_oer.c
        ${GENERATED_DIR}/OCTET_STRING_oer.c
        ${GENERATED_DIR}/NativeInteger_oer.c
        ${GENERATED_DIR}/constr_CHOICE_oer.c
        ${GENERATED_DIR}/constr_SEQUENCE_oer.c
        ${GENERATED_DIR}/constr_SET_OF_oer.c
        ${GENERATED_DIR}/OPEN_TYPE_oer.c
    )

    # Custom command to run asn1c
    add_custom_command(
        OUTPUT ${EXPECTED_GENERATED_FILES}
        COMMAND ${CMAKE_COMMAND} -E remove_directory ${GENERATED_DIR}
        COMMAND ${CMAKE_COMMAND} -E make_directory ${GENERATED_DIR}
        COMMAND asn1c -D ${GENERATED_DIR} ${ASN1_FILES}
        DEPENDS ${ASN1_FILES}
        WORKING_DIRECTORY ${ASN1_SOURCE_DIR}
        COMMENT "Generating ASN.1 parser for ${PROTOCOL_NAME}"
    )

    # Create library target with generated sources
    add_library(asn1_${PROTOCOL_NAME} STATIC ${EXPECTED_GENERATED_FILES})

    # Set include directories
    target_include_directories(asn1_${PROTOCOL_NAME} PUBLIC ${GENERATED_DIR})

    # Set compiler flags to suppress warnings from generated code
    target_compile_options(asn1_${PROTOCOL_NAME} PRIVATE
        -Wno-unused-parameter
        -Wno-unused-variable
        -Wno-unused-function
        -Wno-sign-compare
        -Wno-missing-field-initializers
        -Wno-type-limits
        -Wno-error
    )

    # Set position independent code for shared library usage
    set_target_properties(asn1_${PROTOCOL_NAME} PROPERTIES POSITION_INDEPENDENT_CODE ON)

    # Export the target name for parent scope
    set(ASN1_${PROTOCOL_NAME}_TARGET asn1_${PROTOCOL_NAME} PARENT_SCOPE)
endfunction()

# Function to generate ASN.1 structure discovery header
function(generate_asn1_discovery_header PROTOCOL_NAME)
    set(PROTOCOL_DIR "${CMAKE_SOURCE_DIR}/test/yaLiteDpi/plugins/asn1/protocols/${PROTOCOL_NAME}")
    set(GENERATED_DIR "${CMAKE_CURRENT_BINARY_DIR}/asn1/generated/${PROTOCOL_NAME}")
    set(DISCOVERY_HEADER "${GENERATED_DIR}/asn1_discovery.h")

    # Find all ASN.1 files for the protocol
    file(GLOB ASN1_FILES "${PROTOCOL_DIR}/*.asn")

    if(NOT ASN1_FILES)
        message(FATAL_ERROR "No ASN.1 files found in ${PROTOCOL_DIR}")
    endif()

    # Use the existing asn1_inspector.py script for discovery header generation
    set(DISCOVERY_SCRIPT "${CMAKE_SOURCE_DIR}/test/yaLiteDpi/plugins/asn1/asn1_inspector.py")

    # Make script executable
    execute_process(COMMAND chmod +x ${DISCOVERY_SCRIPT})

    # Generate discovery header
    add_custom_command(
        OUTPUT ${DISCOVERY_HEADER}
        COMMAND python3 ${DISCOVERY_SCRIPT} ${PROTOCOL_NAME} ${DISCOVERY_HEADER} ${ASN1_FILES}
        DEPENDS ${ASN1_FILES} ${DISCOVERY_SCRIPT}
        COMMENT "Generating ASN.1 discovery header for ${PROTOCOL_NAME}"
    )

    # Add to generated files
    set(DISCOVERY_TARGET_NAME asn1_discovery_${PROTOCOL_NAME})
    add_custom_target(${DISCOVERY_TARGET_NAME} DEPENDS ${DISCOVERY_HEADER})

    # Export for parent scope
    set(ASN1_DISCOVERY_HEADER_${PROTOCOL_NAME} ${DISCOVERY_HEADER} PARENT_SCOPE)
    set(ASN1_DISCOVERY_TARGET_${PROTOCOL_NAME} ${DISCOVERY_TARGET_NAME} PARENT_SCOPE)
endfunction()
