#include <gtest/gtest.h>
#include <yaEngineNext/nxt_engine.h>
#include <yaEngineNext/nxt_mbuf.h>
#include <yaProtoRecord/precord.h>
#include <yaBasicUtils/allocator.h>

// ASN.1 includes for direct ASN.1 parsing tests
extern "C" {
#include "asn1_discovery.h"
#include "ber_decoder.h"
}

// Test data extracted from tshark output of b6300a.cap
// Frame 1: SNMP GetRequest
static const uint8_t snmp_get_request_data[] = {
    // SNMP Message: version=0, community="public", get-request
    0x30, 0x2e,                                     // SEQUENCE, length 46
    0x02, 0x01, 0x00,                               // INTEGER version-1 (0)
    0x04, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, // OCTET STRING "public"
    0xa0, 0x21,                                     // GetRequest-PDU, length 33
    0x02, 0x01, 0x26,                               // INTEGER request-id (38)
    0x02, 0x01, 0x00,                               // INTEGER error-status (0)
    0x02, 0x01, 0x00,                               // INTEGER error-index (0)
    0x30, 0x16,                                     // SEQUENCE variable-bindings, length 22
    0x30, 0x14,                                     // SEQUENCE VarBind, length 20
    0x06, 0x0a, 0x2b, 0x06, 0x01, 0x02, 0x01, 0x01, 0x02, 0x00, // OID 1.3.6.1.2.1.1.2.0
    0x05, 0x00                                      // NULL value
};

// Frame 2: SNMP GetResponse (partial data for testing)
static const uint8_t snmp_get_response_data[] = {
    // SNMP Message: version=0, community="public", get-response
    0x30, 0x54,                                     // SEQUENCE, length 84
    0x02, 0x01, 0x00,                               // INTEGER version-1 (0)
    0x04, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, // OCTET STRING "public"
    0xa2, 0x47,                                     // GetResponse-PDU, length 71
    0x02, 0x01, 0x26,                               // INTEGER request-id (38)
    0x02, 0x01, 0x00,                               // INTEGER error-status (0)
    0x02, 0x01, 0x00,                               // INTEGER error-index (0)
    0x30, 0x3c,                                     // SEQUENCE variable-bindings, length 60
    0x30, 0x3a,                                     // SEQUENCE VarBind, length 58
    0x06, 0x0a, 0x2b, 0x06, 0x01, 0x02, 0x01, 0x01, 0x02, 0x00, // OID 1.3.6.1.2.1.1.2.0
    0x06, 0x2c, 0x2b, 0x06, 0x01, 0x04, 0x01, 0x8f, 0x51, 0x01, // OID value (enterprise OID)
    0x01, 0x01, 0x02, 0x00, 0x8f, 0x51, 0x01, 0x02, 0x01, 0x01,
    0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
    0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
    0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01
};

// Additional test data from test_snmp_dissector.c
// Test data: SNMP GetRequest message (from real pcap) - compact version
static const uint8_t test_get_request[] = {
    0x30, 0x26, 0x02, 0x01, 0x00, 0x04, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
    0xa0, 0x19, 0x02, 0x01, 0x26, 0x02, 0x01, 0x00, 0x02, 0x01, 0x00, 0x30, 0x0e,
    0x30, 0x0c, 0x06, 0x08, 0x2b, 0x06, 0x01, 0x02, 0x01, 0x01, 0x01, 0x00, 0x05, 0x00
};

// Test data: SNMP GetResponse message with string value (simplified)
static const uint8_t test_get_response[] = {
    0x30, 0x2a,                                     // SEQUENCE, length 42
    0x02, 0x01, 0x00,                               // INTEGER version-1 (0)
    0x04, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, // OCTET STRING "public"
    0xa2, 0x1d,                                     // GetResponse-PDU, length 29
    0x02, 0x01, 0x26,                               // INTEGER request-id (38)
    0x02, 0x01, 0x00,                               // INTEGER error-status (0)
    0x02, 0x01, 0x00,                               // INTEGER error-index (0)
    0x30, 0x12,                                     // SEQUENCE variable-bindings, length 18
    0x30, 0x10,                                     // SEQUENCE VarBind, length 16
    0x06, 0x08, 0x2b, 0x06, 0x01, 0x02, 0x01, 0x01, 0x01, 0x00, // OID 1.3.6.1.2.1.1.1.0
    0x04, 0x04, 0x74, 0x65, 0x73, 0x74              // OCTET STRING "test"
};

class SNMPAsn1Test : public ::testing::Test {
protected:
    void SetUp() override {
        alloc = ya_allocator_get_default();
        nxt_engine_config_t config = {.linkName = "eth", .trailerName = nullptr};
        engine = nxt_engine_create(&config);
        ASSERT_NE(engine, nullptr);
    }

    void TearDown() override {
        if (engine) {
            nxt_engine_destroy(engine);
        }
    }

    ya_allocator_t* alloc;
    nxt_engine_t* engine;
};

// ========== ASN.1 Direct Parsing Tests ==========
// These tests use the ASN.1 generated code directly to validate parsing

TEST_F(SNMPAsn1Test, ASN1_ParseGetRequest) {
    // Test direct ASN.1 parsing of GetRequest message
    Message_t *message = nullptr;
    asn_dec_rval_t result = ber_decode(0, &asn_DEF_Message, (void **)&message,
                                       test_get_request, sizeof(test_get_request));

    ASSERT_EQ(result.code, RC_OK);
    ASSERT_EQ(result.consumed, sizeof(test_get_request));
    ASSERT_NE(message, nullptr);

    // Check version
    EXPECT_EQ(message->version, 0);

    // Check community
    EXPECT_EQ(message->community.size, 6);
    EXPECT_EQ(memcmp(message->community.buf, "public", 6), 0);

    // Check PDU type
    EXPECT_EQ(message->data.present, PDUs_PR_get_request);

    // Check PDU fields
    PDU_t *pdu = &message->data.choice.get_request;
    EXPECT_EQ(pdu->request_id, 38);
    EXPECT_EQ(pdu->error_status, 0);
    EXPECT_EQ(pdu->error_index, 0);

    // Check variable bindings
    EXPECT_EQ(pdu->variable_bindings.list.count, 1);
    VarBind_t *varbind = pdu->variable_bindings.list.array[0];
    ASSERT_NE(varbind, nullptr);

    // Check OID (1.3.6.1.2.1.1.1.0)
    EXPECT_EQ(varbind->name.size, 8);

    // Check value (should be NULL)
    EXPECT_EQ(varbind->value.present, ObjectSyntax_PR_simple);
    EXPECT_EQ(varbind->value.choice.simple.present, SimpleSyntax_PR_empty);

    ASN_STRUCT_FREE(asn_DEF_Message, message);
}

TEST_F(SNMPAsn1Test, ASN1_ParseGetResponse) {
    // Test direct ASN.1 parsing of GetResponse message
    Message_t *message = nullptr;
    asn_dec_rval_t result = ber_decode(0, &asn_DEF_Message, (void **)&message,
                                       test_get_response, sizeof(test_get_response));

    ASSERT_EQ(result.code, RC_OK);
    ASSERT_EQ(result.consumed, sizeof(test_get_response));
    ASSERT_NE(message, nullptr);

    // Check version
    EXPECT_EQ(message->version, 0);

    // Check community
    EXPECT_EQ(message->community.size, 6);
    EXPECT_EQ(memcmp(message->community.buf, "public", 6), 0);

    // Check PDU type
    EXPECT_EQ(message->data.present, PDUs_PR_get_response);

    // Check PDU fields
    PDU_t *pdu = &message->data.choice.get_response;
    EXPECT_EQ(pdu->request_id, 38);
    EXPECT_EQ(pdu->error_status, 0);
    EXPECT_EQ(pdu->error_index, 0);

    // Check variable bindings
    EXPECT_EQ(pdu->variable_bindings.list.count, 1);
    VarBind_t *varbind = pdu->variable_bindings.list.array[0];
    ASSERT_NE(varbind, nullptr);

    // Check OID (1.3.6.1.2.1.1.1.0)
    EXPECT_EQ(varbind->name.size, 8);

    // Check value (should be string "test")
    EXPECT_EQ(varbind->value.present, ObjectSyntax_PR_simple);
    EXPECT_EQ(varbind->value.choice.simple.present, SimpleSyntax_PR_string);
    EXPECT_EQ(varbind->value.choice.simple.choice.string.size, 4);
    EXPECT_EQ(memcmp(varbind->value.choice.simple.choice.string.buf, "test", 4), 0);

    ASN_STRUCT_FREE(asn_DEF_Message, message);
}

TEST_F(SNMPAsn1Test, ASN1_HandleInvalidData) {
    // Test ASN.1 parsing with invalid BER data
    static const uint8_t invalid_data[] = {0xFF, 0xFF, 0xFF, 0xFF};

    Message_t *message = nullptr;
    asn_dec_rval_t result = ber_decode(0, &asn_DEF_Message, (void **)&message,
                                       invalid_data, sizeof(invalid_data));

    EXPECT_NE(result.code, RC_OK);
    // Note: message might not be NULL even on failure, so we just check the return code
    if (message) {
        ASN_STRUCT_FREE(asn_DEF_Message, message);
    }
}

TEST_F(SNMPAsn1Test, ASN1_ValidateOIDStructure) {
    // Test OID structure validation using ASN.1 parsing
    Message_t *message = nullptr;
    asn_dec_rval_t result = ber_decode(0, &asn_DEF_Message, (void **)&message,
                                       test_get_request, sizeof(test_get_request));

    ASSERT_EQ(result.code, RC_OK);
    ASSERT_NE(message, nullptr);

    // Navigate to the variable binding
    PDU_t *pdu = &message->data.choice.get_request;
    VarBind_t *varbind = pdu->variable_bindings.list.array[0];
    ASSERT_NE(varbind, nullptr);

    // Validate OID structure
    EXPECT_GT(varbind->name.size, 0);
    EXPECT_NE(varbind->name.buf, nullptr);

    // First byte should encode first two arcs: 1*40 + 3 = 43 = 0x2b
    EXPECT_EQ(varbind->name.buf[0], 0x2b);

    ASN_STRUCT_FREE(asn_DEF_Message, message);
}

TEST_F(SNMPAsn1Test, ASN1_ValidateRequestIDMatching) {
    // Test that request and response have matching request IDs
    Message_t *req_message = nullptr;
    Message_t *resp_message = nullptr;

    // Parse request
    asn_dec_rval_t req_result = ber_decode(0, &asn_DEF_Message, (void **)&req_message,
                                           test_get_request, sizeof(test_get_request));
    ASSERT_EQ(req_result.code, RC_OK);
    ASSERT_NE(req_message, nullptr);

    // Parse response
    asn_dec_rval_t resp_result = ber_decode(0, &asn_DEF_Message, (void **)&resp_message,
                                            test_get_response, sizeof(test_get_response));
    ASSERT_EQ(resp_result.code, RC_OK);
    ASSERT_NE(resp_message, nullptr);

    // Both should have the same request ID (38)
    PDU_t *req_pdu = &req_message->data.choice.get_request;
    PDU_t *resp_pdu = &resp_message->data.choice.get_response;

    EXPECT_EQ(req_pdu->request_id, 38);
    EXPECT_EQ(resp_pdu->request_id, 38);
    EXPECT_EQ(req_pdu->request_id, resp_pdu->request_id);

    ASN_STRUCT_FREE(asn_DEF_Message, req_message);
    ASN_STRUCT_FREE(asn_DEF_Message, resp_message);
}
