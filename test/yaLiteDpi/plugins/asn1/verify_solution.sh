#!/bin/bash

# ASN.1协议开发解决方案验证脚本
# 验证所有组件是否正常工作

# set -e  # 注释掉，因为我们需要处理测试失败的情况

echo "🔍 ASN.1协议开发解决方案验证"
echo "=================================="
echo "验证时间: $(date)"
echo "工作目录: $(pwd)"
echo ""

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 计数器
TESTS_PASSED=0
TESTS_FAILED=0
WARNINGS=0

# 日志文件
LOG_FILE="/tmp/asn1_verification_$(date +%Y%m%d_%H%M%S).log"
echo "详细日志: $LOG_FILE"
echo ""

# 测试函数
test_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ PASS${NC}: $2"
        echo "PASS: $2" >> "$LOG_FILE"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: $2"
        echo "FAIL: $2" >> "$LOG_FILE"
        ((TESTS_FAILED++))
    fi
}

# 警告函数
test_warning() {
    echo -e "${YELLOW}⚠️  WARN${NC}: $1"
    echo "WARN: $1" >> "$LOG_FILE"
    ((WARNINGS++))
}

# 信息函数
test_info() {
    echo -e "${BLUE}ℹ️  INFO${NC}: $1"
    echo "INFO: $1" >> "$LOG_FILE"
}

# 详细测试函数
detailed_test() {
    local test_name="$1"
    local test_command="$2"
    local success_message="$3"
    local failure_message="$4"

    echo -e "${CYAN}🔍 测试: $test_name${NC}"

    if eval "$test_command" >> "$LOG_FILE" 2>&1; then
        test_result 0 "$success_message"
    else
        test_result 1 "$failure_message"
    fi
}

# ============================================================================
# 环境检查
# ============================================================================

echo -e "${BLUE}📋 环境检查${NC}"
echo "============================================================================"

# 检查必要的工具
detailed_test "Python3可用性" \
    "which python3" \
    "Python3已安装" \
    "Python3未找到"

detailed_test "Make工具可用性" \
    "which make" \
    "Make工具已安装" \
    "Make工具未找到"

detailed_test "ASN1C工具可用性" \
    "which asn1c" \
    "asn1c工具已安装" \
    "asn1c工具未找到"

# 检查目录结构
test_info "检查目录结构..."
if [ -d "test/yaLiteDpi/plugins/asn1" ]; then
    test_result 0 "ASN.1目录结构存在"
else
    test_result 1 "ASN.1目录结构缺失"
fi

if [ -d "test/yaLiteDpi/plugins/asn1/protocols/snmp" ]; then
    test_result 0 "SNMP协议目录存在"
else
    test_result 1 "SNMP协议目录缺失"
fi

echo ""

# ============================================================================
# 核心功能测试
# ============================================================================

echo -e "${BLUE}🔧 核心功能测试${NC}"
echo "============================================================================"

# 1. ASN.1检查工具测试
detailed_test "ASN.1检查工具基本功能" \
    "python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py test/yaLiteDpi/plugins/asn1/protocols/snmp/snmp.asn > /tmp/asn1_check.out 2>&1" \
    "ASN.1检查工具运行正常" \
    "ASN.1检查工具运行失败"

# 2. 结构名称预测验证
test_info "验证结构名称预测..."
if [ -f "/tmp/asn1_check.out" ]; then
    if grep -q "Message_t" /tmp/asn1_check.out && grep -q "asn_DEF_Message" /tmp/asn1_check.out; then
        test_result 0 "结构名称预测正确"
    else
        test_result 1 "结构名称预测失败"
    fi

    # 检查连字符处理
    if grep -q "Trap_PDU_t" /tmp/asn1_check.out; then
        test_result 0 "连字符转换处理正确"
    else
        test_result 1 "连字符转换处理失败"
    fi

    # 检查CHOICE类型识别
    if grep -q "PDUs_PR" /tmp/asn1_check.out; then
        test_result 0 "CHOICE类型识别正确"
    else
        test_result 1 "CHOICE类型识别失败"
    fi
else
    test_result 1 "无法验证结构名称预测（输出文件不存在）"
fi

# 3. 构建系统测试
test_info "检查构建系统..."
if [ -d "build" ]; then
    test_result 0 "构建目录存在"
else
    test_warning "构建目录不存在，尝试创建..."
    mkdir -p build
    cd build && cmake .. && cd ..
fi

# 4. 发现头文件生成测试
detailed_test "发现头文件生成" \
    "make -C build asn1_discovery_snmp" \
    "发现头文件生成成功" \
    "发现头文件生成失败"

test_info "验证发现头文件内容..."
DISCOVERY_HEADER="build/test/yaLiteDpi/plugins/asn1/generated/snmp/asn1_discovery.h"
if [ -f "$DISCOVERY_HEADER" ]; then
    test_result 0 "发现头文件存在"

    # 检查头文件包含
    if grep -q '#include "Trap-PDU.h"' "$DISCOVERY_HEADER"; then
        test_result 0 "头文件包含正确（连字符处理）"
    else
        test_result 1 "头文件包含错误"
    fi

    # 检查结构定义注释
    if grep -q "Structure: Message_t" "$DISCOVERY_HEADER"; then
        test_result 0 "结构定义注释正确"
    else
        test_result 1 "结构定义注释缺失"
    fi

    # 检查使用示例
    if grep -q "Usage Examples:" "$DISCOVERY_HEADER"; then
        test_result 0 "使用示例包含正确"
    else
        test_result 1 "使用示例缺失"
    fi
else
    test_result 1 "发现头文件不存在"
fi

# ============================================================================
# 编译和链接测试
# ============================================================================

echo -e "${BLUE}🔨 编译和链接测试${NC}"
echo "============================================================================"

# 5. 完整构建测试
detailed_test "完整项目构建" \
    "make -C build" \
    "项目构建成功" \
    "项目构建失败"

# 6. SNMP插件检查
test_info "检查SNMP插件..."
if [ -f "bin/plugins/yaNxtDissector_snmp.so" ]; then
    test_result 0 "SNMP插件编译成功"

    # 检查插件大小
    plugin_size=$(stat -c%s "bin/plugins/yaNxtDissector_snmp.so" 2>/dev/null || echo "0")
    if [ "$plugin_size" -gt 1000 ]; then
        test_result 0 "SNMP插件大小正常 (${plugin_size} bytes)"
    else
        test_warning "SNMP插件大小异常 (${plugin_size} bytes)"
    fi
else
    test_result 1 "SNMP插件编译失败"
fi

# 7. ASN.1库链接验证
test_info "验证ASN.1库链接..."
if [ -f "bin/plugins/yaNxtDissector_snmp.so" ]; then
    if command -v ldd >/dev/null 2>&1; then
        if ldd bin/plugins/yaNxtDissector_snmp.so 2>/dev/null | grep -q "not found"; then
            test_result 1 "ASN.1库链接有问题"
        else
            test_result 0 "ASN.1库链接正常"
        fi
    else
        test_warning "ldd命令不可用，跳过链接检查"
    fi
else
    test_result 1 "无法验证ASN.1库链接（插件不存在）"
fi

# 8. ASN.1静态库检查
test_info "检查ASN.1静态库..."
# 查找可能的静态库位置
ASN1_LIB_PATHS=(
    "build/test/yaLiteDpi/plugins/libasn1_snmp.a"
    "build/test/yaLiteDpi/plugins/asn1/libasn1_snmp.a"
    "build/test/yaLiteDpi/plugins/CMakeFiles/asn1_snmp.dir/asn1/generated/snmp/libasn1_snmp.a"
    "build/libasn1_snmp.a"
)

ASN1_LIB_FOUND=false
for lib_path in "${ASN1_LIB_PATHS[@]}"; do
    if [ -f "$lib_path" ]; then
        test_result 0 "ASN.1静态库存在: $lib_path"
        ASN1_LIB_FOUND=true

        # 检查库大小
        lib_size=$(stat -c%s "$lib_path" 2>/dev/null || echo "0")
        if [ "$lib_size" -gt 10000 ]; then
            test_result 0 "ASN.1静态库大小正常 (${lib_size} bytes)"
        else
            test_warning "ASN.1静态库大小异常 (${lib_size} bytes)"
        fi
        break
    fi
done

if [ "$ASN1_LIB_FOUND" = false ]; then
    test_result 1 "ASN.1静态库不存在"
fi

echo ""

# ============================================================================
# 文档和工具测试
# ============================================================================

echo -e "${BLUE}📚 文档和工具测试${NC}"
echo "============================================================================"

# 9. 文档完整性检查
test_info "检查文档完整性..."
docs_count=0
docs_total=0

# 检查各个文档文件
docs_files=(
    "test/yaLiteDpi/plugins/asn1/README.md"
    "test/yaLiteDpi/plugins/asn1/COMPREHENSIVE_GUIDE.md"
)

for doc_file in "${docs_files[@]}"; do
    ((docs_total++))
    if [ -f "$doc_file" ]; then
        ((docs_count++))
        test_result 0 "文档存在: $(basename "$doc_file")"
    else
        test_result 1 "文档缺失: $(basename "$doc_file")"
    fi
done

if [ $docs_count -eq $docs_total ]; then
    test_result 0 "所有文档完整 ($docs_count/$docs_total)"
else
    test_warning "部分文档缺失 ($docs_count/$docs_total)"
fi

# 10. 工具脚本检查
test_info "检查工具脚本..."
if [ -f "test/yaLiteDpi/plugins/asn1/asn1_inspector.py" ]; then
    test_result 0 "ASN.1检查工具存在"

    # 检查脚本权限
    if [ -x "test/yaLiteDpi/plugins/asn1/asn1_inspector.py" ]; then
        test_result 0 "ASN.1检查工具可执行"
    else
        test_warning "ASN.1检查工具不可执行"
    fi
else
    test_result 1 "ASN.1检查工具缺失"
fi

if [ -f "test/yaLiteDpi/plugins/asn1/verify_solution.sh" ]; then
    test_result 0 "验证脚本存在"
else
    test_result 1 "验证脚本缺失"
fi

echo ""

# ============================================================================
# 新协议添加流程测试
# ============================================================================

echo -e "${BLUE}🆕 新协议添加流程测试${NC}"
echo "============================================================================"

test_info "测试新协议添加流程..."

# 创建测试协议目录
TEST_PROTOCOL_DIR="test/yaLiteDpi/plugins/asn1/protocols/test_protocol"
mkdir -p "$TEST_PROTOCOL_DIR" 2>/dev/null

# 创建简单ASN.1文件
cat > "$TEST_PROTOCOL_DIR/test.asn" << 'EOF'
TestProtocol DEFINITIONS ::= BEGIN

TestMessage ::= SEQUENCE {
    version INTEGER,
    data OCTET STRING,
    options TestOptions
}

TestOptions ::= CHOICE {
    simple [0] IMPLICIT INTEGER,
    complex [1] IMPLICIT TestComplex
}

TestComplex ::= SEQUENCE {
    id INTEGER,
    name OCTET STRING
}

END
EOF

if [ -f "$TEST_PROTOCOL_DIR/test.asn" ]; then
    test_result 0 "测试ASN.1文件创建成功"
else
    test_result 1 "测试ASN.1文件创建失败"
fi

# 测试结构预测
detailed_test "新协议结构预测" \
    "python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py $TEST_PROTOCOL_DIR/test.asn > /tmp/test_protocol_check.out 2>&1" \
    "新协议结构预测成功" \
    "新协议结构预测失败"

# 验证预测结果
if [ -f "/tmp/test_protocol_check.out" ]; then
    if grep -q "TestMessage_t" /tmp/test_protocol_check.out; then
        test_result 0 "TestMessage结构预测正确"
    else
        test_result 1 "TestMessage结构预测失败"
    fi

    if grep -q "TestOptions_PR" /tmp/test_protocol_check.out; then
        test_result 0 "CHOICE类型预测正确"
    else
        test_result 1 "CHOICE类型预测失败"
    fi

    if grep -q "asn_DEF_TestComplex" /tmp/test_protocol_check.out; then
        test_result 0 "复杂类型预测正确"
    else
        test_result 1 "复杂类型预测失败"
    fi
fi

# 测试发现头文件生成
detailed_test "新协议发现头文件生成" \
    "python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py test_protocol /tmp/test_discovery.h $TEST_PROTOCOL_DIR/test.asn" \
    "新协议发现头文件生成成功" \
    "新协议发现头文件生成失败"

if [ -f "/tmp/test_discovery.h" ]; then
    if grep -q '#include "TestMessage.h"' /tmp/test_discovery.h; then
        test_result 0 "发现头文件内容正确"
    else
        test_result 1 "发现头文件内容错误"
    fi
fi

# 清理测试文件
test_info "清理测试文件..."
rm -rf "$TEST_PROTOCOL_DIR"
rm -f /tmp/test_protocol_check.out /tmp/test_discovery.h
test_result 0 "测试文件清理完成"

# ============================================================================
# 性能和兼容性测试
# ============================================================================

echo -e "${BLUE}⚡ 性能和兼容性测试${NC}"
echo "============================================================================"

# 检查生成文件数量
test_info "检查生成文件数量..."
if [ -d "build/test/yaLiteDpi/plugins/asn1/generated/snmp" ]; then
    generated_files=$(find build/test/yaLiteDpi/plugins/asn1/generated/snmp -name "*.c" -o -name "*.h" | wc -l)
    if [ "$generated_files" -gt 50 ]; then
        test_result 0 "ASN.1生成文件数量正常 ($generated_files 个文件)"
    else
        test_warning "ASN.1生成文件数量较少 ($generated_files 个文件)"
    fi
else
    test_result 1 "ASN.1生成目录不存在"
fi

# 检查CMake集成
test_info "检查CMake集成..."
if grep -q "ASN1_PROTOCOLS" test/yaLiteDpi/plugins/protocols.cmake; then
    test_result 0 "CMake协议配置正确"
else
    test_result 1 "CMake协议配置缺失"
fi

if grep -q "generate_asn1_parser" test/yaLiteDpi/plugins/asn1/asn1_functions.cmake; then
    test_result 0 "CMake生成函数存在"
else
    test_result 1 "CMake生成函数缺失"
fi

# 检查单元测试
test_info "检查单元测试..."
if [ -f "test/unit/snmp_asn1_test.cpp" ]; then
    test_result 0 "SNMP单元测试存在"
else
    test_warning "SNMP单元测试缺失"
fi

if [ -f "bin/yaNxtUnitTest" ]; then
    test_result 0 "单元测试可执行文件存在"
else
    test_warning "单元测试可执行文件缺失"
fi

echo ""

# ============================================================================
# 最终汇总和建议
# ============================================================================

echo -e "${BLUE}📊 测试结果汇总${NC}"
echo "============================================================================"

# 计算总测试数
TOTAL_TESTS=$((TESTS_PASSED + TESTS_FAILED))

echo "验证完成时间: $(date)"
echo "详细日志文件: $LOG_FILE"
echo ""
echo -e "✅ 通过: ${GREEN}$TESTS_PASSED${NC}"
echo -e "❌ 失败: ${RED}$TESTS_FAILED${NC}"
echo -e "⚠️  警告: ${YELLOW}$WARNINGS${NC}"
echo -e "📊 总计: $TOTAL_TESTS"
echo ""

# 计算成功率
if [ $TOTAL_TESTS -gt 0 ]; then
    SUCCESS_RATE=$((TESTS_PASSED * 100 / TOTAL_TESTS))
    echo -e "成功率: ${GREEN}${SUCCESS_RATE}%${NC}"
else
    echo -e "成功率: ${RED}0%${NC}"
fi

echo ""

# 根据结果给出不同的反馈
if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 恭喜！所有测试通过！${NC}"
    echo -e "${GREEN}ASN.1协议开发解决方案工作正常。${NC}"
    echo ""

    if [ $WARNINGS -gt 0 ]; then
        echo -e "${YELLOW}⚠️  注意: 有 $WARNINGS 个警告，建议检查。${NC}"
        echo ""
    fi

    echo -e "${CYAN}📚 使用指南:${NC}"
    echo "  • 完整指南: cat test/yaLiteDpi/plugins/asn1/COMPREHENSIVE_GUIDE.md"
    echo "  • 原始文档: cat test/yaLiteDpi/plugins/asn1/README.md"
    echo ""
    echo -e "${CYAN}🔧 常用命令:${NC}"
    echo "  • 结构预测: python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py your_file.asn"
    echo "  • 生成发现头文件: make -C build asn1_discovery_YOUR_PROTOCOL"
    echo "  • 完整构建: make -C build"
    echo "  • 重新验证: ./test/yaLiteDpi/plugins/asn1/verify_solution.sh"
    echo ""
    echo -e "${GREEN}✨ 您现在可以开始开发新的ASN.1协议了！${NC}"

elif [ $TESTS_FAILED -le 3 ]; then
    echo -e "${YELLOW}⚠️  部分测试失败，但核心功能可能正常。${NC}"
    echo -e "${YELLOW}建议检查失败的测试项目。${NC}"
    echo ""
    echo -e "${CYAN}🔧 故障排除建议:${NC}"
    echo "  • 检查构建环境: make -C build clean && make -C build"
    echo "  • 验证工具安装: which python3 asn1c make"
    echo "  • 查看详细日志: cat $LOG_FILE"
    echo "  • 重新运行验证: ./test/yaLiteDpi/plugins/asn1/verify_solution.sh"

else
    echo -e "${RED}❌ 多个测试失败，需要检查配置。${NC}"
    echo ""
    echo -e "${CYAN}🔧 故障排除步骤:${NC}"
    echo "  1. 检查必要工具是否安装:"
    echo "     sudo apt-get install asn1c python3 build-essential cmake"
    echo ""
    echo "  2. 重新构建项目:"
    echo "     rm -rf build && mkdir build && cd build && cmake .. && make"
    echo ""
    echo "  3. 检查目录结构:"
    echo "     ls -la test/yaLiteDpi/plugins/asn1/"
    echo ""
    echo "  4. 查看详细错误:"
    echo "     cat $LOG_FILE"
    echo ""
    echo "  5. 重新运行验证:"
    echo "     ./test/yaLiteDpi/plugins/asn1/verify_solution.sh"
fi

echo ""
echo "============================================================================"

# 清理临时文件
rm -f /tmp/asn1_check.out

# 根据结果设置退出码
if [ $TESTS_FAILED -eq 0 ]; then
    exit 0
elif [ $TESTS_FAILED -le 3 ]; then
    exit 1
else
    exit 2
fi
