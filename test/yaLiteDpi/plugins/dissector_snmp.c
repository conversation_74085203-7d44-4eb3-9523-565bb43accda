#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>
#include <arpa/inet.h>

// Include ASN.1 generated headers (will be generated at build time)
#include "asn1_discovery.h"

// #include "Message.h"
// #include "PDUs.h"
// #include "PDU.h"
// #include "VarBindList.h"
// #include "VarBind.h"
// #include "ObjectName.h"
// #include "ObjectSyntax.h"
// #include "Trap-PDU.h"
// #include "ber_decoder.h"

#define PROTO_NAME     "snmp"

// Helper function to convert ASN.1 OBJECT IDENTIFIER to string
static char* oid_to_string(const OBJECT_IDENTIFIER_t *oid, char *buffer, size_t buffer_size)
{
    if (!oid || !buffer || buffer_size == 0) {
        return NULL;
    }

    buffer[0] = '\0';

    if (oid->size == 0) {
        snprintf(buffer, buffer_size, "0.0");
        return buffer;
    }

    // Parse OID according to ITU-T X.690 encoding rules
    const uint8_t *data = oid->buf;
    size_t data_len = oid->size;
    char temp_buffer[512] = {0};
    size_t pos = 0;

    // First byte encodes first two arcs: first_arc = byte / 40, second_arc = byte % 40
    if (data_len > 0) {
        uint8_t first_byte = data[0];
        uint32_t first_arc = first_byte / 40;
        uint32_t second_arc = first_byte % 40;

        // Handle special case where first arc might be 2
        if (first_arc > 2) {
            first_arc = 2;
            second_arc = first_byte - 80;
        }

        pos += snprintf(temp_buffer + pos, sizeof(temp_buffer) - pos, "%u.%u", first_arc, second_arc);
    }

    // Parse remaining arcs
    size_t i = 1;
    while (i < data_len && pos < sizeof(temp_buffer) - 1) {
        uint32_t arc_value = 0;

        // Parse variable length encoding
        do {
            if (i >= data_len) break;
            arc_value = (arc_value << 7) | (data[i] & 0x7F);
            i++;
        } while (i <= data_len && (data[i-1] & 0x80));

        pos += snprintf(temp_buffer + pos, sizeof(temp_buffer) - pos, ".%u", arc_value);
    }

    // Create final formatted string with symbolic representation
    // Use a more conservative approach to avoid format-truncation warnings
    size_t temp_len = strlen(temp_buffer);
    if (temp_len > 0 && temp_len < buffer_size / 2) {
        // Safe to add symbolic representation
        int written = snprintf(buffer, buffer_size, "%s (iso%s)", temp_buffer, temp_buffer + 1);
        if (written < 0 || (size_t)written >= buffer_size) {
            // Fallback to numeric only if snprintf failed
            strncpy(buffer, temp_buffer, buffer_size - 1);
            buffer[buffer_size - 1] = '\0';
        }
    } else {
        // If buffer is too small or temp_buffer is too long, just copy the numeric OID
        strncpy(buffer, temp_buffer, buffer_size - 1);
        buffer[buffer_size - 1] = '\0';
    }
    return buffer;
}

// Helper function to extract string from OCTET_STRING
static char* octet_string_to_string(const OCTET_STRING_t *octet_str, char *buffer, size_t buffer_size)
{
    if (!octet_str || !buffer || buffer_size == 0) {
        return NULL;
    }

    buffer[0] = '\0';

    if (octet_str->size == 0) {
        return buffer;
    }

    // Convert to hex string representation
    size_t hex_len = octet_str->size * 2;
    if (hex_len + 1 > buffer_size) {
        hex_len = buffer_size - 1;
    }

    for (size_t i = 0; i < octet_str->size && i * 2 < hex_len; i++) {
        snprintf(buffer + i * 2, buffer_size - i * 2, "%02x", octet_str->buf[i]);
    }

    return buffer;
}

// Helper function to convert hex octet string to readable ASCII string
static char* octet_string_to_ascii(const OCTET_STRING_t *octet_str, char *buffer, size_t buffer_size)
{
    if (!octet_str || !buffer || buffer_size == 0) {
        return NULL;
    }

    buffer[0] = '\0';

    if (octet_str->size == 0) {
        return buffer;
    }

    // Check if all bytes are printable ASCII
    bool is_printable = true;
    for (size_t i = 0; i < octet_str->size; i++) {
        if (octet_str->buf[i] < 32 || octet_str->buf[i] > 126) {
            is_printable = false;
            break;
        }
    }

    if (is_printable) {
        // Copy as ASCII string
        size_t copy_len = (octet_str->size < buffer_size - 1) ? octet_str->size : buffer_size - 1;
        memcpy(buffer, octet_str->buf, copy_len);
        buffer[copy_len] = '\0';
    } else {
        // Convert to hex representation
        octet_string_to_string(octet_str, buffer, buffer_size);
    }

    return buffer;
}

// Helper function to process variable bindings
static void process_varbind_list(const VarBindList_t *varbind_list, precord_t *precord)
{
    if (!varbind_list || !precord) {
        return;
    }

    ya_fvalue_t* fvArray = precord_put(precord, "varbind_array", array);

    for (int i = 0; i < varbind_list->list.count; i++) {
        VarBind_t *varbind = varbind_list->list.array[i];
        if (!varbind) continue;

        char oid_str[512] = {0};
        char value_str[512] = {0};
        char value_type[64] = {0};
        char ascii_str[256] = {0};

        // Convert OID to string
        oid_to_string(&varbind->name, oid_str, sizeof(oid_str));

        // Process value based on ObjectSyntax
        switch (varbind->value.present) {
            case ObjectSyntax_PR_simple:
                // Handle SimpleSyntax
                switch (varbind->value.choice.simple.present) {
                    case SimpleSyntax_PR_number:
                        snprintf(value_str, sizeof(value_str), "%ld", varbind->value.choice.simple.choice.number);
                        snprintf(value_type, sizeof(value_type), "Integer32");
                        break;
                    case SimpleSyntax_PR_string:
                        octet_string_to_string(&varbind->value.choice.simple.choice.string, value_str, sizeof(value_str));
                        octet_string_to_ascii(&varbind->value.choice.simple.choice.string, ascii_str, sizeof(ascii_str));
                        snprintf(value_type, sizeof(value_type), "OctetString");
                        break;
                    case SimpleSyntax_PR_object:
                        oid_to_string(&varbind->value.choice.simple.choice.object, value_str, sizeof(value_str));
                        snprintf(value_type, sizeof(value_type), "OID");
                        break;
                    case SimpleSyntax_PR_empty:
                        snprintf(value_str, sizeof(value_str), "NULL");
                        snprintf(value_type, sizeof(value_type), "Null");
                        break;
                    default:
                        snprintf(value_str, sizeof(value_str), "unknown_simple");
                        snprintf(value_type, sizeof(value_type), "Unknown");
                        break;
                }
                break;
            case ObjectSyntax_PR_application_wide:
                // Handle ApplicationSyntax
                switch (varbind->value.choice.application_wide.present) {
                    case ApplicationSyntax_PR_ipAddress:
                        {
                            OCTET_STRING_t *ip_octets = &varbind->value.choice.application_wide.choice.ipAddress;
                            if (ip_octets->size == 4) {
                                snprintf(value_str, sizeof(value_str), "%d.%d.%d.%d",
                                    ip_octets->buf[0], ip_octets->buf[1],
                                    ip_octets->buf[2], ip_octets->buf[3]);
                            } else {
                                octet_string_to_string(ip_octets, value_str, sizeof(value_str));
                            }
                            snprintf(value_type, sizeof(value_type), "IpAddress");
                        }
                        break;
                    case ApplicationSyntax_PR_counter:
                        snprintf(value_str, sizeof(value_str), "%lu",
                            varbind->value.choice.application_wide.choice.counter);
                        snprintf(value_type, sizeof(value_type), "Counter32");
                        break;
                    case ApplicationSyntax_PR_gauge:
                        snprintf(value_str, sizeof(value_str), "%lu",
                            varbind->value.choice.application_wide.choice.gauge);
                        snprintf(value_type, sizeof(value_type), "Gauge32");
                        break;
                    case ApplicationSyntax_PR_ticks:
                        snprintf(value_str, sizeof(value_str), "%lu",
                            varbind->value.choice.application_wide.choice.ticks);
                        snprintf(value_type, sizeof(value_type), "TimeTicks");
                        break;
                    case ApplicationSyntax_PR_arbitrary:
                        octet_string_to_string(&varbind->value.choice.application_wide.choice.arbitrary,
                            value_str, sizeof(value_str));
                        snprintf(value_type, sizeof(value_type), "Opaque");
                        break;
                    default:
                        snprintf(value_str, sizeof(value_str), "unknown_application");
                        snprintf(value_type, sizeof(value_type), "ApplicationSyntax");
                        break;
                }
                break;
            default:
                snprintf(value_str, sizeof(value_str), "unknown");
                snprintf(value_type, sizeof(value_type), "Unknown");
                break;
        }

        // Add to array
        ya_fvalue_t* fvTable = precord_sub_append(precord, fvArray, table, YA_FT_TABLE);
        precord_sub_put(precord, fvTable, "oid", string, YA_FT_STRING, oid_str);
        precord_sub_put(precord, fvTable, "value", string, YA_FT_STRING, value_str);
        precord_sub_put(precord, fvTable, "value_type", string, YA_FT_STRING, value_type);

        // Add ASCII representation for octet strings
        if (strlen(ascii_str) > 0) {
            precord_sub_put(precord, fvTable, "value_ascii", string, YA_FT_STRING, ascii_str);
        }
    }
}

static
int snmp_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf)
{
    // Check minimum length for SNMP message
    if (nxt_mbuf_get_length(mbuf) < 10) {
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    precord_t *precord = nxt_session_create_record(engine, session);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    // Get raw data from mbuf
    const uint8_t *data = nxt_mbuf_get_raw(mbuf, 0);
    size_t data_len = nxt_mbuf_get_length(mbuf);

    // Decode SNMP message using asn1c
    Message_t *message = NULL;
    asn_dec_rval_t decode_result;

    decode_result = ber_decode(0, &asn_DEF_Message, (void **)&message, data, data_len);

    if (decode_result.code != RC_OK) {
        // Decoding failed
        nxt_session_destroy_record(engine, session, precord);
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    // Successfully decoded, consumed bytes = decode_result.consumed
    int lret = (int)decode_result.consumed;

    // Extract basic message information
    precord_put(precord, "version", uinteger, message->version);

    // Extract community string
    char community_str[256] = {0};
    octet_string_to_string(&message->community, community_str, sizeof(community_str));
    precord_put(precord, "community", string, community_str);

    // Process PDU based on type
    switch (message->data.present) {
        case PDUs_PR_get_request:
            precord_put(precord, "pdu_type", string, "GetRequest");
            {
                PDU_t *pdu = &message->data.choice.get_request;
                precord_put(precord, "request_id", uinteger, pdu->request_id);
                precord_put(precord, "error_status", uinteger, pdu->error_status);
                precord_put(precord, "error_index", uinteger, pdu->error_index);
                process_varbind_list(&pdu->variable_bindings, precord);
            }
            break;

        case PDUs_PR_get_next_request:
            precord_put(precord, "pdu_type", string, "GetNextRequest");
            {
                PDU_t *pdu = &message->data.choice.get_next_request;
                precord_put(precord, "request_id", uinteger, pdu->request_id);
                precord_put(precord, "error_status", uinteger, pdu->error_status);
                precord_put(precord, "error_index", uinteger, pdu->error_index);
                process_varbind_list(&pdu->variable_bindings, precord);
            }
            break;

        case PDUs_PR_get_response:
            precord_put(precord, "pdu_type", string, "GetResponse");
            {
                PDU_t *pdu = &message->data.choice.get_response;
                precord_put(precord, "request_id", uinteger, pdu->request_id);
                precord_put(precord, "error_status", uinteger, pdu->error_status);
                precord_put(precord, "error_index", uinteger, pdu->error_index);
                process_varbind_list(&pdu->variable_bindings, precord);
            }
            break;

        case PDUs_PR_set_request:
            precord_put(precord, "pdu_type", string, "SetRequest");
            {
                PDU_t *pdu = &message->data.choice.set_request;
                precord_put(precord, "request_id", uinteger, pdu->request_id);
                precord_put(precord, "error_status", uinteger, pdu->error_status);
                precord_put(precord, "error_index", uinteger, pdu->error_index);
                process_varbind_list(&pdu->variable_bindings, precord);
            }
            break;

        case PDUs_PR_trap:
            precord_put(precord, "pdu_type", string, "Trap");
            {
                Trap_PDU_t *trap = &message->data.choice.trap;
                char enterprise_str[512] = {0};
                oid_to_string(&trap->enterprise, enterprise_str, sizeof(enterprise_str));
                precord_put(precord, "enterprise", string, enterprise_str);

                // Agent address (NetworkAddress is OCTET STRING of size 4 for IPv4)
                if (trap->agent_addr.size == 4) {
                    char ip_str[16];
                    snprintf(ip_str, sizeof(ip_str), "%d.%d.%d.%d",
                        trap->agent_addr.buf[0], trap->agent_addr.buf[1],
                        trap->agent_addr.buf[2], trap->agent_addr.buf[3]);
                    precord_put(precord, "agent_addr", string, ip_str);
                }

                precord_put(precord, "generic_trap", uinteger, trap->generic_trap);
                precord_put(precord, "specific_trap", uinteger, trap->specific_trap);
                precord_put(precord, "time_stamp", uinteger, trap->time_stamp);
                process_varbind_list(&trap->variable_bindings, precord);
            }
            break;

        case PDUs_PR_getBulkRequest:
            precord_put(precord, "pdu_type", string, "GetBulkRequest");
            {
                BulkPDU_t *bulk_pdu = &message->data.choice.getBulkRequest;
                precord_put(precord, "request_id", uinteger, bulk_pdu->request_id);
                precord_put(precord, "non_repeaters", uinteger, bulk_pdu->non_repeaters);
                precord_put(precord, "max_repetitions", uinteger, bulk_pdu->max_repetitions);
                process_varbind_list(&bulk_pdu->variable_bindings, precord);
            }
            break;

        case PDUs_PR_informRequest:
            precord_put(precord, "pdu_type", string, "InformRequest");
            {
                PDU_t *pdu = &message->data.choice.informRequest;
                precord_put(precord, "request_id", uinteger, pdu->request_id);
                precord_put(precord, "error_status", uinteger, pdu->error_status);
                precord_put(precord, "error_index", uinteger, pdu->error_index);
                process_varbind_list(&pdu->variable_bindings, precord);
            }
            break;

        case PDUs_PR_snmpV2_trap:
            precord_put(precord, "pdu_type", string, "SNMPv2-Trap");
            {
                PDU_t *pdu = &message->data.choice.snmpV2_trap;
                precord_put(precord, "request_id", uinteger, pdu->request_id);
                precord_put(precord, "error_status", uinteger, pdu->error_status);
                precord_put(precord, "error_index", uinteger, pdu->error_index);
                process_varbind_list(&pdu->variable_bindings, precord);
            }
            break;

        case PDUs_PR_report:
            precord_put(precord, "pdu_type", string, "Report");
            {
                PDU_t *pdu = &message->data.choice.report;
                precord_put(precord, "request_id", uinteger, pdu->request_id);
                precord_put(precord, "error_status", uinteger, pdu->error_status);
                precord_put(precord, "error_index", uinteger, pdu->error_index);
                process_varbind_list(&pdu->variable_bindings, precord);
            }
            break;

        default:
            precord_put(precord, "pdu_type", string, "Unknown");
            break;
    }

    nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);

    // Free the decoded message
    ASN_STRUCT_FREE(asn_DEF_Message, message);

    nxt_session_destroy_record(engine, session, precord);
    return lret;
}
static int snmp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db) {
  /* 注册 schema */
  pschema_t *pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "snmp");

  // Basic SNMP message fields
  pschema_register_field(pschema, "version", YA_FT_UINT32, "SNMP Version");
  pschema_register_field(pschema, "community", YA_FT_STRING, "Community String");
  pschema_register_field(pschema, "pdu_type", YA_FT_STRING, "PDU Type");

  // PDU fields
  pschema_register_field(pschema, "request_id", YA_FT_UINT32, "Request ID");
  pschema_register_field(pschema, "error_status", YA_FT_UINT32, "Error Status");
  pschema_register_field(pschema, "error_index", YA_FT_UINT32, "Error Index");

  // GetBulk-specific fields
  pschema_register_field(pschema, "non_repeaters", YA_FT_UINT32, "Non Repeaters");
  pschema_register_field(pschema, "max_repetitions", YA_FT_UINT32, "Max Repetitions");

  // Trap-specific fields
  pschema_register_field(pschema, "enterprise", YA_FT_STRING, "Enterprise OID");
  pschema_register_field(pschema, "agent_addr", YA_FT_STRING, "Agent Address");
  pschema_register_field(pschema, "generic_trap", YA_FT_UINT32, "Generic Trap Type");
  pschema_register_field(pschema, "specific_trap", YA_FT_UINT32, "Specific Trap");
  pschema_register_field(pschema, "time_stamp", YA_FT_UINT32, "Time Stamp");

  // Variable bindings array
  pschema_register_field(pschema, "varbind_array", YA_FT_ARRAY, "Variable Bindings");

  // Variable binding fields (for table entries)
  pschema_register_field(pschema, "oid", YA_FT_STRING, "Object Identifier");
  pschema_register_field(pschema, "value", YA_FT_STRING, "Value");
  pschema_register_field(pschema, "value_type", YA_FT_STRING, "Value Type");
  pschema_register_field(pschema, "value_ascii", YA_FT_STRING, "ASCII Value");

  // SNMP v3 and extended fields
  pschema_register_field(pschema, "identifier", YA_FT_UINT32, "identifier");
  pschema_register_field(pschema, "context_name", YA_FT_STRING, "context_name");
  pschema_register_field(pschema, "context_engine", YA_FT_UINT32, "context_engine");
  pschema_register_field(pschema, "msgReportFlag", YA_FT_UINT32, "msgReportFlag");
  pschema_register_field(pschema, "msgEncryptFlag", YA_FT_UINT32, "msgEncryptFlag");
  pschema_register_field(pschema, "msgAuthFlag", YA_FT_UINT32, "msgAuthFlag");
  pschema_register_field(pschema, "sec_mode", YA_FT_UINT32, "sec_mode");
  pschema_register_field(pschema, "engine_boots", YA_FT_UINT32, "engine_boots");
  pschema_register_field(pschema, "engine_time", YA_FT_UINT32, "engine_time");
  pschema_register_field(pschema, "engine_max_msg_size", YA_FT_UINT32, "engine_max_msg_size");
  pschema_register_field(pschema, "engine_id", YA_FT_BYTES, "engine_id");
  pschema_register_field(pschema, "engineIdFormat", YA_FT_UINT32, "engineIdFormat");
  pschema_register_field(pschema, "engineIdData", YA_FT_BYTES, "engineIdData");
  pschema_register_field(pschema, "engineIdDataStr", YA_FT_STRING, "engineIdDataStr");
  pschema_register_field(pschema, "sysName", YA_FT_STRING, "sysName");
  pschema_register_field(pschema, "sysDesc", YA_FT_STRING, "sysDesc");
  pschema_register_field(pschema, "Total_OID", YA_FT_UINT32, "Total_OID");

  return 0;
}

static nxt_dissector_def_t gDissectorDef = {
    .name = "snmp",
    .type = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = snmp_schema_reg,
    .dissectFun = snmp_dissect,
    .mountAt =
        {
            NXT_MNT_NUMBER("udp", 161),
            NXT_MNT_NUMBER("udp", 162),
            NXT_MNT_END,
        },
};

NXT_DISSECTOR_INIT(snmp) { nxt_dissector_register(&gDissectorDef); }
