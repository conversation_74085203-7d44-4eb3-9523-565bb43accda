# ASN.1协议开发快速参考

## 🚀 快速开始

### 1. 添加新协议（3步骤）
```bash
# 1. 创建协议目录
mkdir test/yaLiteDpi/plugins/asn1/protocols/YOUR_PROTOCOL

# 2. 添加ASN.1文件
cp your_protocol.asn test/yaLiteDpi/plugins/asn1/protocols/YOUR_PROTOCOL/

# 3. 注册协议
echo "YOUR_PROTOCOL" >> test/yaLiteDpi/plugins/protocols.cmake
```

### 2. 预览生成的结构
```bash
python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py your_protocol.asn
```

### 3. 构建并获取发现头文件
```bash
make -C build
# 发现头文件位置：
# build/test/yaLiteDpi/plugins/asn1/generated/YOUR_PROTOCOL/asn1_discovery.h
```

## 📋 ASN.1命名规则

| ASN.1定义 | 生成的C结构 | 类型描述符 | 枚举（CHOICE） |
|-----------|-------------|------------|----------------|
| `Message ::= SEQUENCE` | `Message_t` | `asn_DEF_Message` | - |
| `PDUs ::= CHOICE` | `PDUs_t` | `asn_DEF_PDUs` | `PDUs_PR` |
| `Trap-PDU ::= SEQUENCE` | `Trap_PDU_t` | `asn_DEF_Trap_PDU` | - |

## 🔧 解析器模板

```c
#include "asn1_discovery.h"  // 包含所有ASN.1结构

static int your_protocol_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf) {
    // 1. 创建记录
    precord_t *precord = nxt_session_create_record(engine, session);
    precord_layer_put_new_layer_cache(precord, "your_protocol");

    // 2. 获取数据
    const uint8_t *data = nxt_mbuf_get_raw(mbuf, 0);
    size_t data_len = nxt_mbuf_get_length(mbuf);

    // 3. 解码ASN.1（使用发现头文件中的结构）
    YourMessage_t *message = NULL;
    asn_dec_rval_t result = ber_decode(0, &asn_DEF_YourMessage,
                                       (void **)&message, data, data_len);

    if (result.code != RC_OK) {
        nxt_session_destroy_record(engine, session, precord);
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    // 4. 提取字段
    precord_put(precord, "field_name", uinteger, message->field_value);

    // 5. 处理CHOICE类型
    switch (message->choice_field.present) {
        case YourChoice_PR_option1:
            // 处理选项1
            break;
        case YourChoice_PR_option2:
            // 处理选项2
            break;
    }

    // 6. 发送事件
    nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);

    // 7. 清理
    ASN_STRUCT_FREE(asn_DEF_YourMessage, message);
    nxt_session_destroy_record(engine, session, precord);
    return NXT_DISSECT_ST_OK;
}
```

## 🛠️ 常用工具命令

### 检查ASN.1结构
```bash
# 详细分析
python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py protocol.asn

# 生成头文件
python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py protocol_name output.h protocol.asn
```

### 构建特定协议
```bash
# 构建所有
make -C build

# 构建特定ASN.1库
make -C build asn1_YOUR_PROTOCOL

# 生成发现头文件
make -C build asn1_discovery_YOUR_PROTOCOL
```

### 调试ASN.1解析
```c
// 打印解析结果
asn_fprint(stdout, &asn_DEF_YourMessage, message);

// 验证约束
char error_buffer[256];
size_t error_size = sizeof(error_buffer);
int check_result = asn_check_constraints(&asn_DEF_YourMessage, message, 
                                         error_buffer, &error_size);
```

## 📁 文件结构

```
test/yaLiteDpi/plugins/asn1/
├── protocols/                    # ASN.1定义文件
│   ├── snmp/snmp.asn
│   └── your_protocol/your_protocol.asn
├── asn1_functions.cmake          # ASN.1构建函数
├── asn1_inspector.py            # 结构发现工具
├── ASN1_DEVELOPMENT_GUIDE.md    # 详细开发指南
└── QUICK_REFERENCE.md           # 本文件

build/test/yaLiteDpi/plugins/asn1/generated/
├── snmp/                        # 生成的SNMP代码
│   ├── Message.h/c
│   ├── PDUs.h/c
│   └── asn1_discovery.h         # 🎯 发现头文件
└── your_protocol/               # 生成的协议代码
    ├── YourMessage.h/c
    └── asn1_discovery.h         # 🎯 发现头文件
```

## ⚡ 快速故障排除

| 问题 | 解决方案 |
|------|----------|
| 未定义符号错误 | 检查协议是否在 `ASN1_PROTOCOLS` 列表中 |
| 解析失败 | 验证ASN.1语法，检查标签冲突 |
| 编译错误 | 确保包含了 `asn1_discovery.h` |
| 内存泄漏 | 确保调用了 `ASN_STRUCT_FREE()` |
| 找不到结构 | 使用 `asn1_inspector.py` 检查生成的结构名称 |

## 🎯 核心优势

✅ **无需猜测结构名称** - 发现头文件提供完整映射  
✅ **标准化开发流程** - 一致的协议添加过程  
✅ **自动化构建** - CMake自动处理ASN.1代码生成  
✅ **调试友好** - 丰富的调试和验证工具  
✅ **文档完整** - 包含使用示例和最佳实践  

## 📞 获取帮助

1. **详细指南**: 查看 `ASN1_DEVELOPMENT_GUIDE.md`
2. **示例代码**: 参考 `EXAMPLE_NEW_PROTOCOL.md`
3. **现有实现**: 学习 `dissector_snmp.c`
4. **工具帮助**: `python3 asn1_inspector.py --help`

---
💡 **提示**: 始终先使用 `asn1_inspector.py` 预览结构，再开始编写解析器代码！
