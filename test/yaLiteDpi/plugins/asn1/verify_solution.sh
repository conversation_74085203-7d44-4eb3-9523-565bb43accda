#!/bin/bash

# ASN.1协议开发解决方案验证脚本
# 验证所有组件是否正常工作

echo "🔍 ASN.1协议开发解决方案验证"
echo "=================================="

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 计数器
TESTS_PASSED=0
TESTS_FAILED=0

# 测试函数
test_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ PASS${NC}: $2"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ FAIL${NC}: $2"
        ((TESTS_FAILED++))
    fi
}

echo "1. 检查ASN.1检查工具..."
python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py test/yaLiteDpi/plugins/asn1/protocols/snmp/snmp.asn > /tmp/asn1_check.out 2>&1
test_result $? "ASN.1检查工具运行"

echo ""
echo "2. 验证结构名称预测..."
if grep -q "Message_t" /tmp/asn1_check.out && grep -q "asn_DEF_Message" /tmp/asn1_check.out; then
    test_result 0 "结构名称预测正确"
else
    test_result 1 "结构名称预测失败"
fi

echo ""
echo "3. 检查发现头文件生成..."
if [ -f "build/test/yaLiteDpi/plugins/asn1/generated/snmp/asn1_discovery.h" ]; then
    test_result 0 "发现头文件存在"
else
    test_result 1 "发现头文件不存在"
fi

echo ""
echo "4. 验证头文件包含正确性..."
if [ -f "build/test/yaLiteDpi/plugins/asn1/generated/snmp/asn1_discovery.h" ]; then
    if grep -q '#include "Trap-PDU.h"' build/test/yaLiteDpi/plugins/asn1/generated/snmp/asn1_discovery.h; then
        test_result 0 "头文件包含正确（连字符处理）"
    else
        test_result 1 "头文件包含错误"
    fi
else
    test_result 1 "无法验证头文件包含"
fi

echo ""
echo "5. 检查SNMP插件编译..."
if [ -f "bin/plugins/yaNxtDissector_snmp.so" ]; then
    test_result 0 "SNMP插件编译成功"
else
    test_result 1 "SNMP插件编译失败"
fi

echo ""
echo "6. 验证ASN.1库链接..."
if ldd bin/plugins/yaNxtDissector_snmp.so 2>/dev/null | grep -q "not found"; then
    test_result 1 "ASN.1库链接有问题"
else
    test_result 0 "ASN.1库链接正常"
fi

echo ""
echo "7. 检查文档完整性..."
docs_count=0
[ -f "test/yaLiteDpi/plugins/asn1/ASN1_DEVELOPMENT_GUIDE.md" ] && ((docs_count++))
[ -f "test/yaLiteDpi/plugins/asn1/EXAMPLE_NEW_PROTOCOL.md" ] && ((docs_count++))
[ -f "test/yaLiteDpi/plugins/asn1/QUICK_REFERENCE.md" ] && ((docs_count++))

if [ $docs_count -eq 3 ]; then
    test_result 0 "文档完整"
else
    test_result 1 "文档不完整 ($docs_count/3)"
fi

echo ""
echo "8. 测试新协议添加流程..."
echo "   创建测试协议目录..."
mkdir -p test/yaLiteDpi/plugins/asn1/protocols/test_protocol 2>/dev/null

echo "   创建简单ASN.1文件..."
cat > test/yaLiteDpi/plugins/asn1/protocols/test_protocol/test.asn << 'EOF'
TestProtocol DEFINITIONS ::= BEGIN

TestMessage ::= SEQUENCE {
    version INTEGER,
    data OCTET STRING
}

END
EOF

echo "   测试结构预测..."
python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py test/yaLiteDpi/plugins/asn1/protocols/test_protocol/test.asn > /tmp/test_protocol_check.out 2>&1

if grep -q "TestMessage_t" /tmp/test_protocol_check.out; then
    test_result 0 "新协议结构预测正常"
else
    test_result 1 "新协议结构预测失败"
fi

echo "   清理测试文件..."
rm -rf test/yaLiteDpi/plugins/asn1/protocols/test_protocol

echo ""
echo "=================================="
echo "📊 测试结果汇总"
echo "=================================="
echo -e "通过: ${GREEN}$TESTS_PASSED${NC}"
echo -e "失败: ${RED}$TESTS_FAILED${NC}"
echo -e "总计: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 所有测试通过！ASN.1协议开发解决方案工作正常。${NC}"
    echo ""
    echo "📚 使用指南:"
    echo "  • 查看详细指南: cat test/yaLiteDpi/plugins/asn1/ASN1_DEVELOPMENT_GUIDE.md"
    echo "  • 快速参考: cat test/yaLiteDpi/plugins/asn1/QUICK_REFERENCE.md"
    echo "  • 示例代码: cat test/yaLiteDpi/plugins/asn1/EXAMPLE_NEW_PROTOCOL.md"
    echo "  • 结构预测: python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py your_file.asn"
    exit 0
else
    echo ""
    echo -e "${RED}❌ 有 $TESTS_FAILED 个测试失败。请检查上述错误。${NC}"
    exit 1
fi
