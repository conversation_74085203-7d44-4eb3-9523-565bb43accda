# 新ASN.1协议开发示例

本文档通过一个具体示例演示如何使用ASN.1发现工具开发新的协议解析器。

## 场景：开发LDAP协议解析器

假设我们要为yaEngineNext添加LDAP协议支持。

### 步骤1: 准备ASN.1定义文件

首先创建LDAP协议目录并添加ASN.1定义：

```bash
mkdir test/yaLiteDpi/plugins/asn1/protocols/ldap
```

创建 `test/yaLiteDpi/plugins/asn1/protocols/ldap/ldap.asn`:

```asn1
LDAP-Protocol DEFINITIONS ::= BEGIN

LDAPMessage ::= SEQUENCE {
    messageID MessageID,
    protocolOp CHOICE {
        bindRequest [0] BindRequest,
        bindResponse [1] BindResponse,
        unbindRequest [2] UnbindRequest,
        searchRequest [3] SearchRequest,
        searchResEntry [4] SearchResultEntry,
        searchResDone [5] SearchResultDone
    },
    controls [0] Controls OPTIONAL
}

MessageID ::= INTEGER (0..maxInt)

BindRequest ::= [APPLICATION 0] SEQUENCE {
    version INTEGER (1..127),
    name LDAPDN,
    authentication CHOICE {
        simple [0] OCTET STRING,
        sasl [3] SaslCredentials
    }
}

BindResponse ::= [APPLICATION 1] SEQUENCE {
    COMPONENTS OF LDAPResult,
    serverSaslCreds [7] OCTET STRING OPTIONAL
}

LDAPDN ::= LDAPString
LDAPString ::= OCTET STRING

maxInt INTEGER ::= 2147483647

END
```

### 步骤2: 注册新协议

编辑 `test/yaLiteDpi/plugins/protocols.cmake`，添加ldap到协议列表：

```cmake
set(ASN1_PROTOCOLS
    snmp
    ldap  # 添加新协议
)
```

### 步骤3: 使用发现工具预览结构

在构建之前，使用ASN.1检查工具预览将要生成的结构：

```bash
cd /home/<USER>/SDX/libyaEngineNext
python3 test/yaLiteDpi/plugins/asn1/asn1_inspector.py \
    test/yaLiteDpi/plugins/asn1/protocols/ldap/ldap.asn
```

输出示例：
```
============================================================
ASN.1 结构预测报告
============================================================

发现 6 个类型定义:

1. LDAPMessage
   C结构体名称: LDAPMessage_t
   类型描述符: asn_DEF_LDAPMessage
   类型: SEQUENCE

2. MessageID
   C结构体名称: MessageID_t
   类型描述符: asn_DEF_MessageID
   类型: INTEGER

3. BindRequest
   C结构体名称: BindRequest_t
   类型描述符: asn_DEF_BindRequest
   类型: SEQUENCE

...

使用示例:
1. 包含头文件:
   #include "LDAPMessage.h"
   #include "ber_decoder.h"

2. 解码ASN.1数据:
   LDAPMessage_t *message = NULL;
   asn_dec_rval_t result = ber_decode(0, &asn_DEF_LDAPMessage,
                                      (void **)&message, data, data_len);
```

### 步骤4: 构建项目生成代码

```bash
cd /home/<USER>/SDX/libyaEngineNext
make -C build
```

构建完成后，检查生成的发现头文件：

```bash
cat build/test/yaLiteDpi/plugins/asn1/generated/ldap/asn1_discovery.h
```

### 步骤5: 实现解析器

创建 `test/yaLiteDpi/plugins/dissector_ldap.c`:

```c
#include <yaEngineNext.h>
#include <yaLiteDpi.h>

// 包含ASN.1发现头文件 - 这里包含了所有需要的结构定义
#include "asn1_discovery.h"

#define PROTO_NAME "ldap"

static int ldap_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf) {
    precord_t *precord = nxt_session_create_record(engine, session);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    // 获取原始数据
    const uint8_t *data = nxt_mbuf_get_raw(mbuf, 0);
    size_t data_len = nxt_mbuf_get_length(mbuf);

    // 使用发现头文件中的结构进行解码
    LDAPMessage_t *ldap_message = NULL;
    asn_dec_rval_t decode_result;

    decode_result = ber_decode(0, &asn_DEF_LDAPMessage, 
                              (void **)&ldap_message, data, data_len);

    if (decode_result.code != RC_OK) {
        nxt_session_destroy_record(engine, session, precord);
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    // 提取字段 - 使用发现头文件中的结构信息
    precord_put(precord, "message_id", uinteger, ldap_message->messageID);

    // 处理协议操作 - 使用发现头文件中的枚举信息
    switch (ldap_message->protocolOp.present) {
        case protocolOp_PR_bindRequest:
            precord_put(precord, "operation", string, "BindRequest");
            // 处理BindRequest特定字段
            break;
        case protocolOp_PR_bindResponse:
            precord_put(precord, "operation", string, "BindResponse");
            // 处理BindResponse特定字段
            break;
        // ... 其他操作类型
        default:
            precord_put(precord, "operation", string, "Unknown");
            break;
    }

    nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);

    // 释放解析的消息 - 使用发现头文件中的描述符
    ASN_STRUCT_FREE(asn_DEF_LDAPMessage, ldap_message);

    nxt_session_destroy_record(engine, session, precord);
    return NXT_DISSECT_ST_OK;
}

// 注册解析器
static nxt_dissector_t ldap_dissector = {
    .name = PROTO_NAME,
    .dissect = ldap_dissect,
};

NXT_DISSECTOR_REGISTER(ldap_dissector);
```

### 步骤6: 验证和调试

1. **编译验证**：
```bash
make -C build
```

2. **结构验证**：
使用调试功能验证解析结果：
```c
// 在解析器中添加调试代码
asn_fprint(stdout, &asn_DEF_LDAPMessage, ldap_message);
```

3. **单元测试**：
创建单元测试验证解析功能。

## 关键优势

### 1. 结构名称透明
- 无需猜测生成的结构名称
- 发现头文件提供完整的映射信息
- 包含使用示例和最佳实践

### 2. 开发效率
- 快速预览ASN.1结构
- 自动生成的包含文件
- 标准化的开发流程

### 3. 维护性
- 集中的协议配置
- 自动化的构建过程
- 一致的错误处理模式

## 故障排除

### 常见问题
1. **编译错误**：检查ASN.1语法和标签冲突
2. **链接错误**：确保协议已添加到ASN1_PROTOCOLS列表
3. **运行时错误**：验证BER数据格式和解析逻辑

### 调试技巧
1. 使用 `asn1_inspector.py` 预览结构
2. 使用 `asn_fprint()` 打印解析结果
3. 检查 `decode_result.code` 和 `decode_result.consumed`

## 总结

通过使用ASN.1发现工具和标准化的开发流程，开发者可以：
- 快速确定生成的结构名称
- 高效实现新的协议解析器
- 维护一致的代码质量
- 减少开发和调试时间

这个解决方案完全解决了"不知道生成的ASN.1结构名称"的问题，为ASN.1协议开发提供了完整的工具链支持。
